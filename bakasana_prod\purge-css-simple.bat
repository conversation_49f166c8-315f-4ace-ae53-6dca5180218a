@echo off
echo 🧹 BAKASANA CSS PURGE - Prosty sposób czyszczenia CSS
echo.

REM Sprawdź czy podano argumenty
if "%1"=="" (
    echo Użycie: purge-css-simple.bat [plik-css] [folder-zawartości] [plik-wyj<PERSON><PERSON><PERSON>]
    echo.
    echo Przykłady:
    echo   purge-css-simple.bat src/styles/main.css "src/**/*.{js,jsx}" src/styles/clean/main.clean.css
    echo   purge-css-simple.bat src/app/globals.css "src/**/*.{js,jsx,ts,tsx}" clean.css
    echo.
    echo Lub użyj domyślnych ustawień:
    echo   purge-css-simple.bat default
    goto :end
)

if "%1"=="default" (
    echo 🔄 Używam domyślnych ustawień...
    npx purgecss --css src/styles/main.css --content "src/**/*.{js,jsx,ts,tsx}" --output src/styles/clean/main-default.css
    echo ✅ Oczyszczony plik zapisany: src/styles/clean/main-default.css
    goto :end
)

REM Użyj podanych argumentów
set CSS_FILE=%1
set CONTENT_PATTERN=%2
set OUTPUT_FILE=%3

if "%OUTPUT_FILE%"=="" set OUTPUT_FILE=clean.css

echo 🔄 Czyszczę CSS...
echo   📁 Plik CSS: %CSS_FILE%
echo   📄 Zawartość: %CONTENT_PATTERN%
echo   💾 Wyjście: %OUTPUT_FILE%
echo.

npx purgecss --css %CSS_FILE% --content %CONTENT_PATTERN% --output %OUTPUT_FILE%

if %ERRORLEVEL% EQU 0 (
    echo ✅ CSS został oczyszczony pomyślnie!
    echo 💾 Zapisano: %OUTPUT_FILE%
) else (
    echo ❌ Błąd podczas czyszczenia CSS
)

:end
pause
