/* =============================================
   🏆 BAKASANA - PREMIUM UTILITIES FOR 9/10 UX
   Advanced utilities for world-class experience
   ============================================= */

/* ===== PREMIUM ANIMATIONS ===== */
.animate-premium-fade-in {
  animation: premiumFadeIn var(--duration-medium) var(--ease-premium) forwards;
}

.animate-premium-slide-up {
  animation: premiumSlideUp var(--duration-medium) var(--ease-premium) forwards;
}

.animate-premium-scale-in {
  animation: premiumScaleIn var(--duration-quick) var(--ease-premium) forwards;
}

@keyframes premiumFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes premiumSlideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes premiumScaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== PREMIUM HOVER EFFECTS ===== */

/* ===== PREMIUM FOCUS STATES ===== */

/* ===== PREMIUM LOADING STATES ===== */

@keyframes premiumPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* ===== PREMIUM GLASS EFFECTS ===== */

/* ===== PREMIUM GRADIENTS ===== */

/* ===== PREMIUM SHADOWS ===== */

/* ===== PREMIUM TYPOGRAPHY ===== */

/* ===== PREMIUM CONTAINERS ===== */

/* ===== PREMIUM GRID SYSTEMS ===== */

/* ===== PREMIUM SCROLL EFFECTS ===== */

/* ===== PREMIUM ACCESSIBILITY ===== */

/* ===== PREMIUM PERFORMANCE ===== */

/* ===== PREMIUM RESPONSIVE ===== */
@media (max-width: 768px) {
  .mobile-optimized {
    font-size: clamp(0.875rem, 4vw, 1rem);
    line-height: 1.6;
    letter-spacing: 0.01em;
  }
}

@media (min-width: 1920px) {
  .desktop-enhanced {
    font-size: clamp(1rem, 1.2vw, 1.125rem);
  }
}

/* ===== PREMIUM PRINT STYLES ===== */

/* ===== PREMIUM REDUCED MOTION ===== */
