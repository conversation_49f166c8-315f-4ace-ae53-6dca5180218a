#!/usr/bin/env node

const { PurgeCSS } = require('purgecss');
const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🧹 BAKASANA CSS PURGE - Usuwanie nieużywanych stylów...\n');

// Konfiguracja ścieżek
const srcDir = path.join(__dirname, '../src');
const stylesDir = path.join(srcDir, 'styles');
const appDir = path.join(srcDir, 'app');
const componentsDir = path.join(srcDir, 'components');

// Znajdź wszystkie pliki CSS do analizy
const cssFiles = [
  path.join(stylesDir, 'main.css'),
  path.join(stylesDir, 'hero.css'),
  path.join(stylesDir, 'sections.css'),
  path.join(stylesDir, 'modern-css.css'),
  path.join(stylesDir, 'advanced-typography.css'),
  path.join(stylesDir, 'microinteractions.css'),
  path.join(stylesDir, 'premium-utilities.css'),
  path.join(appDir, 'enhanced-globals.css'),
  path.join(appDir, 'bakasana-visuals.css')
].filter(file => fs.existsSync(file));

// Znajdź wszystkie pliki HTML/JSX/JS do analizy
const contentFiles = [
  ...glob.sync(path.join(srcDir, '**/*.{js,jsx,ts,tsx,html}'), { 
    ignore: ['**/node_modules/**', '**/.next/**', '**/backup/**'] 
  })
];

console.log(`📁 Znaleziono ${cssFiles.length} plików CSS do analizy:`);
cssFiles.forEach(file => console.log(`   - ${path.relative(process.cwd(), file)}`));

console.log(`\n📄 Znaleziono ${contentFiles.length} plików zawartości do analizy\n`);

// Konfiguracja PurgeCSS
const purgeConfig = {
  content: contentFiles,
  css: cssFiles,
  defaultExtractor: content => {
    // Wyciągnij klasy CSS, zmienne CSS i selektory
    const broadMatches = content.match(/[^<>"'`\s]*[^<>"'`\s:]/g) || [];
    const innerMatches = content.match(/[^<>"'`\s.()]*[^<>"'`\s.():]/g) || [];
    return broadMatches.concat(innerMatches);
  },
  safelist: [
    // Zachowaj ważne klasy systemowe
    /^(html|body|root)$/,
    /^:root$/,
    /^@/,
    /^--/,
    // Zachowaj klasy Tailwind
    /^(sm|md|lg|xl|2xl):/,
    /^(hover|focus|active|disabled):/,
    // Zachowaj klasy animacji
    /^(animate|transition|transform)/,
    // Zachowaj klasy komponentów
    /^(btn|card|nav|hero|section)/,
    // Zachowaj klasy responsywne
    /^(mobile|tablet|desktop)/,
    // Zachowaj klasy stanu
    /^(is-|has-|js-)/,
    // Zachowaj klasy AOS
    /^aos/,
    // Zachowaj klasy Framer Motion
    /^motion/,
    // Zachowaj klasy Map
    /^mapbox/,
    /^map/,
    // Zachowaj klasy kalendarza
    /^rbc/,
    // Zachowaj klasy toast
    /^toast/,
    // Zachowaj klasy skip-link
    'skip-link',
    // Zachowaj klasy luxury
    /^luxury/,
    // Zachowaj klasy unified
    /^unified/
  ],
  blocklist: [
    // Usuń niepotrzebne klasy
    /^unused/,
    /^deprecated/,
    /^old-/
  ]
};

async function purgeCSSFiles() {
  try {
    console.log('🔄 Rozpoczynam analizę i czyszczenie CSS...\n');
    
    const results = await new PurgeCSS().purge(purgeConfig);
    
    // Utwórz folder dla oczyszczonych plików
    const cleanDir = path.join(__dirname, '../src/styles/clean');
    if (!fs.existsSync(cleanDir)) {
      fs.mkdirSync(cleanDir, { recursive: true });
    }
    
    let totalOriginalSize = 0;
    let totalCleanSize = 0;
    
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const originalFile = cssFiles[i];
      const originalContent = fs.readFileSync(originalFile, 'utf8');
      const cleanContent = result.css;
      
      // Oblicz rozmiary
      const originalSize = Buffer.byteLength(originalContent, 'utf8');
      const cleanSize = Buffer.byteLength(cleanContent, 'utf8');
      const savings = originalSize - cleanSize;
      const savingsPercent = ((savings / originalSize) * 100).toFixed(1);
      
      totalOriginalSize += originalSize;
      totalCleanSize += cleanSize;
      
      // Zapisz oczyszczony plik
      const fileName = path.basename(originalFile, '.css');
      const cleanFile = path.join(cleanDir, `${fileName}.clean.css`);
      fs.writeFileSync(cleanFile, cleanContent);
      
      console.log(`✅ ${path.basename(originalFile)}:`);
      console.log(`   Oryginalny: ${(originalSize / 1024).toFixed(1)} KB`);
      console.log(`   Oczyszczony: ${(cleanSize / 1024).toFixed(1)} KB`);
      console.log(`   Oszczędność: ${(savings / 1024).toFixed(1)} KB (${savingsPercent}%)`);
      console.log(`   Zapisano: ${path.relative(process.cwd(), cleanFile)}\n`);
    }
    
    // Podsumowanie
    const totalSavings = totalOriginalSize - totalCleanSize;
    const totalSavingsPercent = ((totalSavings / totalOriginalSize) * 100).toFixed(1);
    
    console.log('📊 PODSUMOWANIE OPTYMALIZACJI:');
    console.log(`   Całkowity rozmiar oryginalny: ${(totalOriginalSize / 1024).toFixed(1)} KB`);
    console.log(`   Całkowity rozmiar oczyszczony: ${(totalCleanSize / 1024).toFixed(1)} KB`);
    console.log(`   Całkowita oszczędność: ${(totalSavings / 1024).toFixed(1)} KB (${totalSavingsPercent}%)`);
    
    // Zapisz raport
    const report = {
      timestamp: new Date().toISOString(),
      files: results.map((result, i) => ({
        file: path.basename(cssFiles[i]),
        originalSize: Buffer.byteLength(fs.readFileSync(cssFiles[i], 'utf8'), 'utf8'),
        cleanSize: Buffer.byteLength(result.css, 'utf8'),
        savings: Buffer.byteLength(fs.readFileSync(cssFiles[i], 'utf8'), 'utf8') - Buffer.byteLength(result.css, 'utf8')
      })),
      totalOriginalSize,
      totalCleanSize,
      totalSavings,
      totalSavingsPercent: parseFloat(totalSavingsPercent)
    };
    
    fs.writeFileSync(
      path.join(__dirname, '../css-purge-report.json'),
      JSON.stringify(report, null, 2)
    );
    
    console.log('\n✨ Optymalizacja CSS zakończona pomyślnie!');
    console.log(`📄 Raport zapisany: css-purge-report.json`);
    
  } catch (error) {
    console.error('❌ Błąd podczas czyszczenia CSS:', error);
    process.exit(1);
  }
}

// Uruchom optymalizację
purgeCSSFiles();
