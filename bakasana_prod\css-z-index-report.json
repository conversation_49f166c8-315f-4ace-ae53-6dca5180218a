{"timestamp": "2025-07-24T19:03:04.970Z", "summary": {"totalZIndexes": 43, "numericZIndexes": 43, "veryHighCount": 5, "highCount": 8, "duplicatesCount": 9, "maxZIndex": 9999, "minZIndex": -1}, "allZIndexes": [{"file": "src\\styles\\touch-targets.css", "line": 355, "selector": ".skip-links", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\premium-utilities.css", "line": 242, "selector": ".skip-link-premium", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\navbar-critical-fix.css", "line": 95, "selector": "nav .hidden.md\\\\:flex::before", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\microinteractions.css", "line": 274, "selector": ".cursor-follower", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\app\\enhanced-globals.css", "line": 453, "selector": ".skip-link", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\typography.css", "line": 397, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\styles\\modern-css.css", "line": 344, "selector": ".anchored-tooltip", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\styles\\main.css", "line": 93, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 34, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 2210, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 620, "selector": ".mobile-menu", "value": "999", "numericValue": 999, "context": "z-index: 999;"}, {"file": "src\\app\\globals.css", "line": 76, "selector": ".whatsapp-float", "value": "998", "numericValue": 998, "context": "z-index: 998; /* Below cookie banner (z-50 = 50) but above content */"}, {"file": "src\\app\\globals.css", "line": 607, "selector": ".mobile-menu-overlay", "value": "998", "numericValue": 998, "context": "z-index: 998;"}, {"file": "src\\styles\\hero.css", "line": 86, "selector": ".hero-content", "value": "20", "numericValue": 20, "context": "z-index: 20;"}, {"file": "src\\styles\\hero.css", "line": 225, "selector": ".hero-side-form", "value": "20", "numericValue": 20, "context": "z-index: 20;"}, {"file": "src\\styles\\touch-targets.css", "line": 438, "selector": ".focus-visible-enabled select:focus-visible", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\styles\\hero.css", "line": 43, "selector": ".hero-gradient-overlay", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\styles\\hero.css", "line": 77, "selector": ".hero-texture-overlay", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\styles\\hero.css", "line": 299, "selector": ".hero-floating-elements", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\app\\globals.css", "line": 2470, "selector": ".about-hero-container", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\app\\globals.css", "line": 3127, "selector": ".about-content-centered", "value": "5", "numericValue": 5, "context": "z-index: 5;"}, {"file": "src\\app\\globals.css", "line": 1058, "selector": ".hero-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 2652, "selector": ".about-photo-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 3022, "selector": ".about-cta-container", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 3190, "selector": ".about-photo-placeholder-small .about-photo-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\bakasana-visuals.css", "line": 117, "selector": ".b<PERSON><PERSON><PERSON>-hero-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 1263, "selector": ".section-smooth", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 1373, "selector": ".destination-card::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 2437, "selector": ".about-page-elegant::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 3671, "selector": ".about-page-integrated section::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 5075, "selector": ".enterprise-card::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\enhanced-globals.css", "line": 332, "selector": ".section-divider-diamond", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\styles\\hero.css", "line": 19, "selector": ".hero-bg", "value": "0", "numericValue": 0, "context": "z-index: 0;"}, {"file": "src\\styles\\hero.css", "line": 31, "selector": ".hero-parallax-layer", "value": "0", "numericValue": 0, "context": "z-index: 0;"}, {"file": "src\\app\\globals.css", "line": 1281, "selector": ".section-smooth::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 1299, "selector": ".section-smooth::after", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 3104, "selector": ".about-cta-button-refined::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 3427, "selector": ".about-journey-button-centered::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 4253, "selector": ".about-cta-button-integrated::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 5110, "selector": ".section-transition::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 5128, "selector": ".section-transition::after", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\enhanced-globals.css", "line": 220, "selector": ".btn-secondary::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\bakasana-visuals.css", "line": 111, "selector": ".b<PERSON><PERSON><PERSON>-hero::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}], "veryHighZIndex": [{"file": "src\\styles\\touch-targets.css", "line": 355, "selector": ".skip-links", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\premium-utilities.css", "line": 242, "selector": ".skip-link-premium", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\navbar-critical-fix.css", "line": 95, "selector": "nav .hidden.md\\\\:flex::before", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\microinteractions.css", "line": 274, "selector": ".cursor-follower", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\app\\enhanced-globals.css", "line": 453, "selector": ".skip-link", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}], "highZIndex": [{"file": "src\\styles\\typography.css", "line": 397, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\styles\\modern-css.css", "line": 344, "selector": ".anchored-tooltip", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\styles\\main.css", "line": 93, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 34, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 2210, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 620, "selector": ".mobile-menu", "value": "999", "numericValue": 999, "context": "z-index: 999;"}, {"file": "src\\app\\globals.css", "line": 76, "selector": ".whatsapp-float", "value": "998", "numericValue": 998, "context": "z-index: 998; /* Below cookie banner (z-50 = 50) but above content */"}, {"file": "src\\app\\globals.css", "line": 607, "selector": ".mobile-menu-overlay", "value": "998", "numericValue": 998, "context": "z-index: 998;"}], "duplicates": [{"value": 0, "items": [{"file": "src\\styles\\hero.css", "line": 19, "selector": ".hero-bg", "value": "0", "numericValue": 0, "context": "z-index: 0;"}, {"file": "src\\styles\\hero.css", "line": 31, "selector": ".hero-parallax-layer", "value": "0", "numericValue": 0, "context": "z-index: 0;"}]}, {"value": 1, "items": [{"file": "src\\app\\globals.css", "line": 1263, "selector": ".section-smooth", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 1373, "selector": ".destination-card::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 2437, "selector": ".about-page-elegant::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 3671, "selector": ".about-page-integrated section::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 5075, "selector": ".enterprise-card::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\enhanced-globals.css", "line": 332, "selector": ".section-divider-diamond", "value": "1", "numericValue": 1, "context": "z-index: 1;"}]}, {"value": 2, "items": [{"file": "src\\app\\globals.css", "line": 1058, "selector": ".hero-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 2652, "selector": ".about-photo-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 3022, "selector": ".about-cta-container", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 3190, "selector": ".about-photo-placeholder-small .about-photo-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\bakasana-visuals.css", "line": 117, "selector": ".b<PERSON><PERSON><PERSON>-hero-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}]}, {"value": 10, "items": [{"file": "src\\styles\\touch-targets.css", "line": 438, "selector": ".focus-visible-enabled select:focus-visible", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\styles\\hero.css", "line": 43, "selector": ".hero-gradient-overlay", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\styles\\hero.css", "line": 77, "selector": ".hero-texture-overlay", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\styles\\hero.css", "line": 299, "selector": ".hero-floating-elements", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\app\\globals.css", "line": 2470, "selector": ".about-hero-container", "value": "10", "numericValue": 10, "context": "z-index: 10;"}]}, {"value": 20, "items": [{"file": "src\\styles\\hero.css", "line": 86, "selector": ".hero-content", "value": "20", "numericValue": 20, "context": "z-index: 20;"}, {"file": "src\\styles\\hero.css", "line": 225, "selector": ".hero-side-form", "value": "20", "numericValue": 20, "context": "z-index: 20;"}]}, {"value": 998, "items": [{"file": "src\\app\\globals.css", "line": 76, "selector": ".whatsapp-float", "value": "998", "numericValue": 998, "context": "z-index: 998; /* Below cookie banner (z-50 = 50) but above content */"}, {"file": "src\\app\\globals.css", "line": 607, "selector": ".mobile-menu-overlay", "value": "998", "numericValue": 998, "context": "z-index: 998;"}]}, {"value": 1000, "items": [{"file": "src\\styles\\typography.css", "line": 397, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\styles\\modern-css.css", "line": 344, "selector": ".anchored-tooltip", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\styles\\main.css", "line": 93, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 34, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 2210, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}]}, {"value": 9999, "items": [{"file": "src\\styles\\touch-targets.css", "line": 355, "selector": ".skip-links", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\premium-utilities.css", "line": 242, "selector": ".skip-link-premium", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\navbar-critical-fix.css", "line": 95, "selector": "nav .hidden.md\\\\:flex::before", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\microinteractions.css", "line": 274, "selector": ".cursor-follower", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\app\\enhanced-globals.css", "line": 453, "selector": ".skip-link", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}]}, {"value": -1, "items": [{"file": "src\\app\\globals.css", "line": 1281, "selector": ".section-smooth::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 1299, "selector": ".section-smooth::after", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 3104, "selector": ".about-cta-button-refined::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 3427, "selector": ".about-journey-button-centered::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 4253, "selector": ".about-cta-button-integrated::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 5110, "selector": ".section-transition::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 5128, "selector": ".section-transition::after", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\enhanced-globals.css", "line": 220, "selector": ".btn-secondary::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\bakasana-visuals.css", "line": 111, "selector": ".b<PERSON><PERSON><PERSON>-hero::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}]}], "recommendations": [{"type": "warning", "title": "Bardzo wysokie wartości z-index", "description": "5 elementów ma z-index > 1000. Rozważ reorganizację warstw.", "items": [{"file": "src\\styles\\touch-targets.css", "line": 355, "selector": ".skip-links", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\premium-utilities.css", "line": 242, "selector": ".skip-link-premium", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\navbar-critical-fix.css", "line": 95, "selector": "nav .hidden.md\\\\:flex::before", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\microinteractions.css", "line": 274, "selector": ".cursor-follower", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\app\\enhanced-globals.css", "line": 453, "selector": ".skip-link", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}]}, {"type": "info", "title": "Wiele duplikatów z-index", "description": "9 wartości z-index się powtarza. Rozważ standaryzację.", "items": [{"value": "0", "count": 2}, {"value": "1", "count": 6}, {"value": "2", "count": 5}]}], "fileResults": [{"file": "src\\styles\\unified-system.css", "zIndexCount": 0, "zIndexes": []}, {"file": "src\\styles\\typography.css", "zIndexCount": 1, "zIndexes": [{"file": "src\\styles\\typography.css", "line": 397, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}]}, {"file": "src\\styles\\touch-targets.css", "zIndexCount": 2, "zIndexes": [{"file": "src\\styles\\touch-targets.css", "line": 355, "selector": ".skip-links", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}, {"file": "src\\styles\\touch-targets.css", "line": 438, "selector": ".focus-visible-enabled select:focus-visible", "value": "10", "numericValue": 10, "context": "z-index: 10;"}]}, {"file": "src\\styles\\sections.css", "zIndexCount": 0, "zIndexes": []}, {"file": "src\\styles\\premium-utilities.css", "zIndexCount": 1, "zIndexes": [{"file": "src\\styles\\premium-utilities.css", "line": 242, "selector": ".skip-link-premium", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}]}, {"file": "src\\styles\\navbar-fix.css", "zIndexCount": 0, "zIndexes": []}, {"file": "src\\styles\\navbar-critical-fix.css", "zIndexCount": 1, "zIndexes": [{"file": "src\\styles\\navbar-critical-fix.css", "line": 95, "selector": "nav .hidden.md\\\\:flex::before", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}]}, {"file": "src\\styles\\modern-css.css", "zIndexCount": 1, "zIndexes": [{"file": "src\\styles\\modern-css.css", "line": 344, "selector": ".anchored-tooltip", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}]}, {"file": "src\\styles\\microinteractions.css", "zIndexCount": 1, "zIndexes": [{"file": "src\\styles\\microinteractions.css", "line": 274, "selector": ".cursor-follower", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}]}, {"file": "src\\styles\\main.css", "zIndexCount": 1, "zIndexes": [{"file": "src\\styles\\main.css", "line": 93, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}]}, {"file": "src\\styles\\hero.css", "zIndexCount": 7, "zIndexes": [{"file": "src\\styles\\hero.css", "line": 19, "selector": ".hero-bg", "value": "0", "numericValue": 0, "context": "z-index: 0;"}, {"file": "src\\styles\\hero.css", "line": 31, "selector": ".hero-parallax-layer", "value": "0", "numericValue": 0, "context": "z-index: 0;"}, {"file": "src\\styles\\hero.css", "line": 43, "selector": ".hero-gradient-overlay", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\styles\\hero.css", "line": 77, "selector": ".hero-texture-overlay", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\styles\\hero.css", "line": 86, "selector": ".hero-content", "value": "20", "numericValue": 20, "context": "z-index: 20;"}, {"file": "src\\styles\\hero.css", "line": 225, "selector": ".hero-side-form", "value": "20", "numericValue": 20, "context": "z-index: 20;"}, {"file": "src\\styles\\hero.css", "line": 299, "selector": ".hero-floating-elements", "value": "10", "numericValue": 10, "context": "z-index: 10;"}]}, {"file": "src\\styles\\critical-images.css", "zIndexCount": 0, "zIndexes": []}, {"file": "src\\styles\\color-migration.css", "zIndexCount": 0, "zIndexes": []}, {"file": "src\\styles\\advanced-typography.css", "zIndexCount": 0, "zIndexes": []}, {"file": "src\\styles\\advanced-grid.css", "zIndexCount": 0, "zIndexes": []}, {"file": "src\\app\\globals.css", "zIndexCount": 23, "zIndexes": [{"file": "src\\app\\globals.css", "line": 34, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 76, "selector": ".whatsapp-float", "value": "998", "numericValue": 998, "context": "z-index: 998; /* Below cookie banner (z-50 = 50) but above content */"}, {"file": "src\\app\\globals.css", "line": 607, "selector": ".mobile-menu-overlay", "value": "998", "numericValue": 998, "context": "z-index: 998;"}, {"file": "src\\app\\globals.css", "line": 620, "selector": ".mobile-menu", "value": "999", "numericValue": 999, "context": "z-index: 999;"}, {"file": "src\\app\\globals.css", "line": 1058, "selector": ".hero-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 1263, "selector": ".section-smooth", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 1281, "selector": ".section-smooth::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 1299, "selector": ".section-smooth::after", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 1373, "selector": ".destination-card::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 2210, "selector": ".skip-link", "value": "1000", "numericValue": 1000, "context": "z-index: 1000;"}, {"file": "src\\app\\globals.css", "line": 2437, "selector": ".about-page-elegant::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 2470, "selector": ".about-hero-container", "value": "10", "numericValue": 10, "context": "z-index: 10;"}, {"file": "src\\app\\globals.css", "line": 2652, "selector": ".about-photo-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 3022, "selector": ".about-cta-container", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 3104, "selector": ".about-cta-button-refined::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 3127, "selector": ".about-content-centered", "value": "5", "numericValue": 5, "context": "z-index: 5;"}, {"file": "src\\app\\globals.css", "line": 3190, "selector": ".about-photo-placeholder-small .about-photo-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}, {"file": "src\\app\\globals.css", "line": 3427, "selector": ".about-journey-button-centered::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 3671, "selector": ".about-page-integrated section::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 4253, "selector": ".about-cta-button-integrated::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 5075, "selector": ".enterprise-card::before", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\globals.css", "line": 5110, "selector": ".section-transition::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\globals.css", "line": 5128, "selector": ".section-transition::after", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}]}, {"file": "src\\app\\enhanced-globals.css", "zIndexCount": 3, "zIndexes": [{"file": "src\\app\\enhanced-globals.css", "line": 220, "selector": ".btn-secondary::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\enhanced-globals.css", "line": 332, "selector": ".section-divider-diamond", "value": "1", "numericValue": 1, "context": "z-index: 1;"}, {"file": "src\\app\\enhanced-globals.css", "line": 453, "selector": ".skip-link", "value": "9999", "numericValue": 9999, "context": "z-index: 9999;"}]}, {"file": "src\\app\\bakasana-visuals.css", "zIndexCount": 2, "zIndexes": [{"file": "src\\app\\bakasana-visuals.css", "line": 111, "selector": ".b<PERSON><PERSON><PERSON>-hero::before", "value": "-1", "numericValue": -1, "context": "z-index: -1;"}, {"file": "src\\app\\bakasana-visuals.css", "line": 117, "selector": ".b<PERSON><PERSON><PERSON>-hero-content", "value": "2", "numericValue": 2, "context": "z-index: 2;"}]}]}