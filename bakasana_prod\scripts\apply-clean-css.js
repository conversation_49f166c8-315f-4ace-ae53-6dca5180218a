#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 BAKASANA CSS OPTIMIZER - Zastosowanie oczyszczonych stylów...\n');

const srcDir = path.join(__dirname, '../src');
const stylesDir = path.join(srcDir, 'styles');
const cleanDir = path.join(stylesDir, 'clean');
const appDir = path.join(srcDir, 'app');

// Mapowanie plików do zastąpienia
const fileMapping = [
  {
    original: path.join(stylesDir, 'main.css'),
    clean: path.join(cleanDir, 'main.clean.css'),
    backup: path.join(stylesDir, 'main.css.backup')
  },
  {
    original: path.join(stylesDir, 'hero.css'),
    clean: path.join(cleanDir, 'hero.clean.css'),
    backup: path.join(stylesDir, 'hero.css.backup')
  },
  {
    original: path.join(stylesDir, 'sections.css'),
    clean: path.join(cleanDir, 'sections.clean.css'),
    backup: path.join(stylesDir, 'sections.css.backup')
  },
  {
    original: path.join(stylesDir, 'modern-css.css'),
    clean: path.join(cleanDir, 'modern-css.clean.css'),
    backup: path.join(stylesDir, 'modern-css.css.backup')
  },
  {
    original: path.join(stylesDir, 'advanced-typography.css'),
    clean: path.join(cleanDir, 'advanced-typography.clean.css'),
    backup: path.join(stylesDir, 'advanced-typography.css.backup')
  },
  {
    original: path.join(stylesDir, 'microinteractions.css'),
    clean: path.join(cleanDir, 'microinteractions.clean.css'),
    backup: path.join(stylesDir, 'microinteractions.css.backup')
  },
  {
    original: path.join(stylesDir, 'premium-utilities.css'),
    clean: path.join(cleanDir, 'premium-utilities.clean.css'),
    backup: path.join(stylesDir, 'premium-utilities.css.backup')
  },
  {
    original: path.join(appDir, 'enhanced-globals.css'),
    clean: path.join(cleanDir, 'enhanced-globals.clean.css'),
    backup: path.join(appDir, 'enhanced-globals.css.backup')
  },
  {
    original: path.join(appDir, 'bakasana-visuals.css'),
    clean: path.join(cleanDir, 'bakasana-visuals.clean.css'),
    backup: path.join(appDir, 'bakasana-visuals.css.backup')
  }
];

function applyCleanCSS() {
  console.log('📋 Zastępowanie plików CSS oczyszczonymi wersjami...\n');
  
  let totalSavings = 0;
  let processedFiles = 0;
  
  fileMapping.forEach(({ original, clean, backup }) => {
    if (fs.existsSync(original) && fs.existsSync(clean)) {
      // Utwórz kopię zapasową
      fs.copyFileSync(original, backup);
      
      // Pobierz rozmiary
      const originalSize = fs.statSync(original).size;
      const cleanSize = fs.statSync(clean).size;
      const savings = originalSize - cleanSize;
      const savingsPercent = ((savings / originalSize) * 100).toFixed(1);
      
      // Zastąp oryginalny plik oczyszczonym
      fs.copyFileSync(clean, original);
      
      console.log(`✅ ${path.basename(original)}:`);
      console.log(`   Oryginalny: ${(originalSize / 1024).toFixed(1)} KB`);
      console.log(`   Oczyszczony: ${(cleanSize / 1024).toFixed(1)} KB`);
      console.log(`   Oszczędność: ${(savings / 1024).toFixed(1)} KB (${savingsPercent}%)`);
      console.log(`   Kopia zapasowa: ${path.basename(backup)}\n`);
      
      totalSavings += savings;
      processedFiles++;
    } else {
      console.log(`⚠️  Pominięto ${path.basename(original)} - brak pliku oryginalnego lub oczyszczonego\n`);
    }
  });
  
  console.log('📊 PODSUMOWANIE ZASTOSOWANIA:');
  console.log(`   📁 Przetworzonych plików: ${processedFiles}`);
  console.log(`   💾 Całkowita oszczędność: ${(totalSavings / 1024).toFixed(1)} KB`);
  console.log(`   🔄 Kopie zapasowe utworzone z rozszerzeniem .backup`);
  
  console.log('\n✨ Oczyszczone style CSS zostały zastosowane!');
  console.log('💡 Aby przywrócić oryginalne pliki, uruchom: npm run css:restore');
}

function restoreOriginalCSS() {
  console.log('🔄 Przywracanie oryginalnych plików CSS...\n');
  
  let restoredFiles = 0;
  
  fileMapping.forEach(({ original, backup }) => {
    if (fs.existsSync(backup)) {
      fs.copyFileSync(backup, original);
      fs.unlinkSync(backup);
      
      console.log(`✅ Przywrócono: ${path.basename(original)}`);
      restoredFiles++;
    }
  });
  
  console.log(`\n📊 Przywrócono ${restoredFiles} plików CSS`);
  console.log('✨ Oryginalne pliki CSS zostały przywrócone!');
}

// Sprawdź argument wiersza poleceń
const action = process.argv[2];

if (action === 'restore') {
  restoreOriginalCSS();
} else {
  applyCleanCSS();
}
