# 🔍 CSS Z-INDEX ANALYSIS REPORT

Raport wygenerowany: 24.07.2025, 21:03:04

## 📊 Podsumowanie

- **Łącznie z-index**: 43
- **<PERSON><PERSON><PERSON> wysokie (>1000)**: 5
- **<PERSON><PERSON><PERSON><PERSON> (100-1000)**: 8
- **Duplikaty**: 9

## 💡 Rekomendacje

### ⚠️ Bardzo wysokie wartości z-index

5 elementów ma z-index > 1000. Rozważ reorganizację warstw.

### 💡 Wiele duplikatów z-index

9 wartości z-index się powtarza. Rozważ standaryzację.

## 🔝 Najwyższe Z-Index

| Wartość | Selektor | Plik | Linia |
|---------|----------|------|-------|
| 9999 | `.skip-links` | src\styles\touch-targets.css | 355 |
| 9999 | `.skip-link-premium` | src\styles\premium-utilities.css | 242 |
| 9999 | `nav .hidden.md\\:flex::before` | src\styles\navbar-critical-fix.css | 95 |
| 9999 | `.cursor-follower` | src\styles\microinteractions.css | 274 |
| 9999 | `.skip-link` | src\app\enhanced-globals.css | 453 |
| 1000 | `.skip-link` | src\styles\typography.css | 397 |
| 1000 | `.anchored-tooltip` | src\styles\modern-css.css | 344 |
| 1000 | `.skip-link` | src\styles\main.css | 93 |
| 1000 | `.skip-link` | src\app\globals.css | 34 |
| 1000 | `.skip-link` | src\app\globals.css | 2210 |

