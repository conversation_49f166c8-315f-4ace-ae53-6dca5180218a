#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🔍 BAKASANA CSS Z-INDEX ANALYZER - Analiza z-index w plikach CSS...\n');

// Konfiguracja ścieżek
const srcDir = path.join(__dirname, '../src');
const stylesDir = path.join(srcDir, 'styles');
const appDir = path.join(srcDir, 'app');

// Znajdź wszystkie pliki CSS
const cssFiles = [
  ...glob.sync(path.join(stylesDir, '*.css').replace(/\\/g, '/')),
  ...glob.sync(path.join(appDir, '*.css').replace(/\\/g, '/')),
  ...glob.sync(path.join(srcDir, 'components/**/*.css').replace(/\\/g, '/'))
].filter(file => !file.includes('node_modules') && !file.includes('.backup') && !file.includes('clean'));

function extractZIndexFromCSS(cssContent, filePath) {
  const zIndexMatches = [];
  const lines = cssContent.split('\n');
  
  lines.forEach((line, lineNumber) => {
    // Znajdź z-index w linii
    const zIndexRegex = /z-index\s*:\s*([^;]+);?/gi;
    let match;
    
    while ((match = zIndexRegex.exec(line)) !== null) {
      const value = match[1].trim();
      const numericValue = parseInt(value);
      
      // Znajdź selektor dla tego z-index
      let selector = 'unknown';
      for (let i = lineNumber; i >= 0; i--) {
        const prevLine = lines[i].trim();
        if (prevLine.includes('{')) {
          const selectorMatch = prevLine.match(/([^{]+)\s*\{/);
          if (selectorMatch) {
            selector = selectorMatch[1].trim();
            break;
          }
        }
      }
      
      zIndexMatches.push({
        file: path.relative(process.cwd(), filePath),
        line: lineNumber + 1,
        selector,
        value,
        numericValue: isNaN(numericValue) ? null : numericValue,
        context: line.trim()
      });
    }
  });
  
  return zIndexMatches;
}

function analyzeZIndexInFiles() {
  console.log(`📁 Analizuję ${cssFiles.length} plików CSS...\n`);
  
  const allZIndexes = [];
  const fileResults = [];
  
  cssFiles.forEach(cssFile => {
    console.log(`🔍 Analizuję: ${path.relative(process.cwd(), cssFile)}`);
    
    try {
      const cssContent = fs.readFileSync(cssFile, 'utf8');
      const zIndexes = extractZIndexFromCSS(cssContent, cssFile);
      
      allZIndexes.push(...zIndexes);
      fileResults.push({
        file: path.relative(process.cwd(), cssFile),
        zIndexCount: zIndexes.length,
        zIndexes
      });
      
      if (zIndexes.length > 0) {
        console.log(`   ✅ Znaleziono ${zIndexes.length} z-index`);
        zIndexes.forEach(z => {
          const warning = z.numericValue > 1000 ? ' ⚠️ BARDZO WYSOKI' : z.numericValue > 100 ? ' 🔶 WYSOKI' : '';
          console.log(`      Linia ${z.line}: ${z.selector} → z-index: ${z.value}${warning}`);
        });
      } else {
        console.log(`   ℹ️  Brak z-index`);
      }
      console.log('');
      
    } catch (error) {
      console.log(`   ❌ Błąd: ${error.message}\n`);
    }
  });
  
  return { allZIndexes, fileResults };
}

function generateZIndexReport(allZIndexes, fileResults) {
  console.log('📊 GENEROWANIE RAPORTU Z-INDEX...\n');
  
  // Sortuj według wartości numerycznej
  const numericZIndexes = allZIndexes.filter(z => z.numericValue !== null);
  numericZIndexes.sort((a, b) => b.numericValue - a.numericValue);
  
  // Znajdź problemy
  const veryHighZIndex = numericZIndexes.filter(z => z.numericValue > 1000);
  const highZIndex = numericZIndexes.filter(z => z.numericValue > 100 && z.numericValue <= 1000);
  const duplicateZIndex = {};
  
  numericZIndexes.forEach(z => {
    const key = z.numericValue;
    if (!duplicateZIndex[key]) duplicateZIndex[key] = [];
    duplicateZIndex[key].push(z);
  });
  
  const duplicates = Object.entries(duplicateZIndex).filter(([key, values]) => values.length > 1);
  
  // Wyświetl podsumowanie
  console.log('🎯 PODSUMOWANIE Z-INDEX W CSS:');
  console.log(`   📊 Łącznie z-index w CSS: ${allZIndexes.length}`);
  console.log(`   🔢 Z wartościami numerycznymi: ${numericZIndexes.length}`);
  console.log(`   ⚠️  Bardzo wysokie (>1000): ${veryHighZIndex.length}`);
  console.log(`   🔶 Wysokie (100-1000): ${highZIndex.length}`);
  console.log(`   🔄 Duplikaty: ${duplicates.length} wartości`);
  
  if (numericZIndexes.length > 0) {
    console.log(`   🔝 Najwyższy z-index: ${numericZIndexes[0].numericValue}`);
    console.log(`   🔻 Najniższy z-index: ${numericZIndexes[numericZIndexes.length - 1].numericValue}`);
  }
  
  // Wyświetl najwyższe z-index
  if (numericZIndexes.length > 0) {
    console.log('\n🔝 NAJWYŻSZE Z-INDEX:');
    numericZIndexes.slice(0, 10).forEach((z, index) => {
      console.log(`   ${index + 1}. ${z.numericValue} - ${z.selector} (${z.file}:${z.line})`);
    });
  }
  
  // Wyświetl duplikaty
  if (duplicates.length > 0) {
    console.log('\n🔄 DUPLIKATY Z-INDEX:');
    duplicates.slice(0, 5).forEach(([value, items]) => {
      console.log(`   z-index: ${value} (${items.length} wystąpień):`);
      items.forEach(item => {
        console.log(`      - ${item.selector} (${item.file}:${item.line})`);
      });
    });
  }
  
  // Generuj rekomendacje
  const recommendations = [];
  
  if (veryHighZIndex.length > 0) {
    recommendations.push({
      type: 'warning',
      title: 'Bardzo wysokie wartości z-index',
      description: `${veryHighZIndex.length} elementów ma z-index > 1000. Rozważ reorganizację warstw.`,
      items: veryHighZIndex.slice(0, 5)
    });
  }
  
  if (duplicates.length > 5) {
    recommendations.push({
      type: 'info',
      title: 'Wiele duplikatów z-index',
      description: `${duplicates.length} wartości z-index się powtarza. Rozważ standaryzację.`,
      items: duplicates.slice(0, 3).map(([value, items]) => ({ value, count: items.length }))
    });
  }
  
  // Zapisz raport
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalZIndexes: allZIndexes.length,
      numericZIndexes: numericZIndexes.length,
      veryHighCount: veryHighZIndex.length,
      highCount: highZIndex.length,
      duplicatesCount: duplicates.length,
      maxZIndex: numericZIndexes.length > 0 ? numericZIndexes[0].numericValue : 0,
      minZIndex: numericZIndexes.length > 0 ? numericZIndexes[numericZIndexes.length - 1].numericValue : 0
    },
    allZIndexes: numericZIndexes,
    veryHighZIndex,
    highZIndex,
    duplicates: duplicates.map(([value, items]) => ({ value: parseInt(value), items })),
    recommendations,
    fileResults
  };
  
  fs.writeFileSync(
    path.join(__dirname, '../css-z-index-report.json'),
    JSON.stringify(report, null, 2)
  );
  
  // Generuj plik z rekomendacjami
  let mdContent = '# 🔍 CSS Z-INDEX ANALYSIS REPORT\n\n';
  mdContent += `Raport wygenerowany: ${new Date().toLocaleString()}\n\n`;
  
  mdContent += '## 📊 Podsumowanie\n\n';
  mdContent += `- **Łącznie z-index**: ${allZIndexes.length}\n`;
  mdContent += `- **Bardzo wysokie (>1000)**: ${veryHighZIndex.length}\n`;
  mdContent += `- **Wysokie (100-1000)**: ${highZIndex.length}\n`;
  mdContent += `- **Duplikaty**: ${duplicates.length}\n\n`;
  
  if (recommendations.length > 0) {
    mdContent += '## 💡 Rekomendacje\n\n';
    recommendations.forEach(rec => {
      const icon = rec.type === 'warning' ? '⚠️' : rec.type === 'error' ? '❌' : '💡';
      mdContent += `### ${icon} ${rec.title}\n\n`;
      mdContent += `${rec.description}\n\n`;
    });
  }
  
  if (numericZIndexes.length > 0) {
    mdContent += '## 🔝 Najwyższe Z-Index\n\n';
    mdContent += '| Wartość | Selektor | Plik | Linia |\n';
    mdContent += '|---------|----------|------|-------|\n';
    numericZIndexes.slice(0, 10).forEach(z => {
      mdContent += `| ${z.numericValue} | \`${z.selector}\` | ${z.file} | ${z.line} |\n`;
    });
    mdContent += '\n';
  }
  
  fs.writeFileSync(
    path.join(__dirname, '../css-z-index-analysis.md'),
    mdContent
  );
  
  console.log('\n✨ Analiza z-index zakończona!');
  console.log('📄 Szczegółowy raport: css-z-index-report.json');
  console.log('📋 Analiza markdown: css-z-index-analysis.md');
}

// Uruchom analizę
const { allZIndexes, fileResults } = analyzeZIndexInFiles();
generateZIndexReport(allZIndexes, fileResults);
