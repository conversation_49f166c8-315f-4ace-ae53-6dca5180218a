module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx,html}',
    './src/app/**/*.{js,jsx,ts,tsx}',
    './src/components/**/*.{js,jsx,ts,tsx}',
    './src/pages/**/*.{js,jsx,ts,tsx}',
    './public/**/*.html'
  ],
  css: [
    './src/styles/main.css',
    './src/styles/hero.css',
    './src/styles/sections.css',
    './src/styles/modern-css.css',
    './src/styles/advanced-typography.css',
    './src/styles/microinteractions.css',
    './src/styles/premium-utilities.css',
    './src/app/enhanced-globals.css',
    './src/app/bakasana-visuals.css'
  ],
  defaultExtractor: content => {
    // Wyciągnij klasy CSS, zmienne CSS i selektory
    const broadMatches = content.match(/[^<>"'`\s]*[^<>"'`\s:]/g) || [];
    const innerMatches = content.match(/[^<>"'`\s.()]*[^<>"'`\s.():]/g) || [];
    return broadMatches.concat(innerMatches);
  },
  safelist: [
    // Zachowaj ważne klasy systemowe
    /^(html|body|root)$/,
    /^:root$/,
    /^@/,
    /^--/,
    // Zachowaj klasy Tailwind
    /^(sm|md|lg|xl|2xl):/,
    /^(hover|focus|active|disabled):/,
    // Zachowaj klasy animacji
    /^(animate|transition|transform)/,
    // Zachowaj klasy komponentów
    /^(btn|card|nav|hero|section)/,
    // Zachowaj klasy responsywne
    /^(mobile|tablet|desktop)/,
    // Zachowaj klasy stanu
    /^(is-|has-|js-)/,
    // Zachowaj klasy bibliotek
    /^aos/,
    /^motion/,
    /^mapbox/,
    /^map/,
    /^rbc/,
    /^toast/,
    'skip-link',
    /^luxury/,
    /^unified/,
    // Zachowaj klasy dynamiczne
    /^dynamic/,
    /^conditional/
  ],
  blocklist: [
    // Usuń niepotrzebne klasy
    /^unused/,
    /^deprecated/,
    /^old-/,
    /^test-/,
    /^debug-/
  ]
};
