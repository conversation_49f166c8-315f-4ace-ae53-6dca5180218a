'use client';

import React, { useState, useEffect } from 'react';
import { motion, useMotionValue, useSpring } from 'framer-motion';

/**
 * LuxuryElements - Subtelne luksusowe elementy UI
 * Złote akcenty, magnetyczne efekty, płynne animacje
 */

// Magnetyczny przycisk z subtelnym złotym akcentem
export function LuxuryButton({
  children,
  className = '',
  intensity = 0.3,
  goldAccent = true,
  as,
  ...props
}) {
  const [isHovered, setIsHovered] = useState(false);

  // Determine the component to render
  const Component = React.useMemo(() => {
    if (!as) return motion.button;
    if (typeof as === 'string') return motion[as] || motion.div;
    if (typeof as === 'function') return motion(as);
    console.warn('LuxuryButton: Invalid "as" prop provided, falling back to motion.button');
    return motion.button;
  }, [as]);
  
  return (
    <Component
      className={`
        relative overflow-hidden transition-all duration-300
        ${goldAccent ? 'hover:shadow-[0_0_20px_rgba(193,155,104,0.3)]' : ''}
        ${className}
      `}
      whileHover={{
        scale: 1.02,
        y: -2
      }}
      whileTap={{
        scale: 0.98
      }}
      transition={{
        type: "spring",
        stiffness: 400,
        damping: 17
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      {...props}
    >
      {/* Subtelny złoty gradient na hover */}
      {goldAccent && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-warm-gold/10 to-transparent"
          initial={{ x: '-100%', opacity: 0 }}
          animate={{ 
            x: isHovered ? '100%' : '-100%',
            opacity: isHovered ? 1 : 0
          }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        />
      )}
      
      {/* Zawartość */}
      <span className="relative z-10">
        {children}
      </span>
    </Component>
  );
}

// Karta z luksusowym hover efektem
export function LuxuryCard({ 
  children, 
  className = '', 
  goldBorder = true,
  floatIntensity = 4,
  ...props 
}) {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <motion.div
      className={`
        relative transition-all duration-500 ease-out
        ${goldBorder ? 'hover:shadow-[0_20px_40px_rgba(139,115,85,0.15)]' : ''}
        ${className}
      `}
      whileHover={{ 
        y: -floatIntensity,
        scale: 1.01
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      {...props}
    >
      {/* Subtelna złota ramka na hover */}
      {goldBorder && (
        <motion.div
          className="absolute inset-0 rounded-lg"
          initial={{ 
            background: 'linear-gradient(45deg, transparent, transparent)',
            opacity: 0 
          }}
          animate={{ 
            background: isHovered 
              ? 'linear-gradient(45deg, rgba(193,155,104,0.1), rgba(184,147,92,0.1), rgba(212,175,122,0.1), rgba(193,155,104,0.1))'
              : 'linear-gradient(45deg, transparent, transparent)',
            opacity: isHovered ? 1 : 0
          }}
          transition={{ duration: 0.5 }}
        />
      )}
      
      {children}
    </motion.div>
  );
}

// Luksusowy separator z animacją
export function LuxurySeparator({ 
  className = '',
  animated = true,
  goldAccent = true 
}) {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.5 }
    );
    
    const element = document.getElementById('luxury-separator');
    if (element) observer.observe(element);
    
    return () => observer.disconnect();
  }, []);
  
  return (
    <div 
      id="luxury-separator"
      className={`flex items-center justify-center py-xl ${className}`}
    >
      <motion.div
        className="flex items-center"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ 
          opacity: isVisible ? 1 : 0,
          scale: isVisible ? 1 : 0.8
        }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        {/* Lewa linia */}
        <motion.div
          className={`h-px w-16 ${goldAccent ? 'bg-gradient-to-r from-transparent to-enterprise-brown' : 'bg-stone-light'}`}
          initial={{ scaleX: 0 }}
          animate={{ scaleX: isVisible ? 1 : 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        />
        
        {/* Centralny element */}
        <motion.div
          className={`mx-6 w-2 h-2 rounded-full ${goldAccent ? 'bg-enterprise-brown' : 'bg-stone-light'}`}
          initial={{ scale: 0, rotate: 0 }}
          animate={{ 
            scale: isVisible ? 1 : 0,
            rotate: isVisible ? 360 : 0
          }}
          transition={{ duration: 0.8, delay: 0.4 }}
        />
        
        {/* Prawa linia */}
        <motion.div
          className={`h-px w-16 ${goldAccent ? 'bg-gradient-to-l from-transparent to-enterprise-brown' : 'bg-stone-light'}`}
          initial={{ scaleX: 0 }}
          animate={{ scaleX: isVisible ? 1 : 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        />
      </motion.div>
    </div>
  );
}

// Luksusowy tooltip
export function LuxuryTooltip({ 
  children, 
  content, 
  position = 'top',
  goldAccent = true 
}) {
  const [isVisible, setIsVisible] = useState(false);
  
  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  };
  
  return (
    <div 
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      
      <motion.div
        className={`
          absolute z-50 px-3 py-2 text-sm text-sanctuary rounded-lg shadow-lg
          ${goldAccent ? 'bg-enterprise-brown' : 'bg-charcoal'}
          ${positionClasses[position]}
        `}
        initial={{ opacity: 0, scale: 0.8, y: position === 'top' ? 10 : -10 }}
        animate={{ 
          opacity: isVisible ? 1 : 0,
          scale: isVisible ? 1 : 0.8,
          y: isVisible ? 0 : (position === 'top' ? 10 : -10)
        }}
        transition={{ duration: 0.2 }}
        style={{ pointerEvents: 'none' }}
      >
        {content}
        
        {/* Strzałka */}
        <div 
          className={`
            absolute w-2 h-2 transform rotate-45
            ${goldAccent ? 'bg-enterprise-brown' : 'bg-charcoal'}
            ${position === 'top' ? 'top-full left-1/2 -translate-x-1/2 -mt-1' : ''}
            ${position === 'bottom' ? 'bottom-full left-1/2 -translate-x-1/2 -mb-1' : ''}
            ${position === 'left' ? 'left-full top-1/2 -translate-y-1/2 -ml-1' : ''}
            ${position === 'right' ? 'right-full top-1/2 -translate-y-1/2 -mr-1' : ''}
          `}
        />
      </motion.div>
    </div>
  );
}

// Luksusowy progress bar
export function LuxuryProgressBar({ 
  progress = 0, 
  className = '',
  goldAccent = true,
  animated = true 
}) {
  return (
    <div className={`w-full bg-stone-light/30 rounded-full h-2 overflow-hidden ${className}`}>
      <motion.div
        className={`h-full rounded-full ${
          goldAccent 
            ? 'bg-gradient-to-r from-enterprise-brown via-terra to-sand' 
            : 'bg-charcoal'
        }`}
        initial={{ width: 0 }}
        animate={{ width: `${progress}%` }}
        transition={{ 
          duration: animated ? 1.5 : 0,
          ease: "easeOut" 
        }}
      />
    </div>
  );
}

// Luksusowy badge
export function LuxuryBadge({ 
  children, 
  variant = 'gold', // 'gold', 'silver', 'bronze'
  size = 'md',
  className = '' 
}) {
  const variants = {
    gold: 'bg-gradient-to-r from-enterprise-brown to-terra text-sanctuary',
    silver: 'bg-gradient-to-r from-stone to-ash text-sanctuary',
    bronze: 'bg-gradient-to-r from-clay to-soft-bronze text-sanctuary'
  };
  
  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };
  
  return (
    <motion.span
      className={`
        inline-flex items-center rounded-full font-inter font-medium
        shadow-lg backdrop-blur-sm
        ${variants[variant]}
        ${sizes[size]}
        ${className}
      `}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      {children}
    </motion.span>
  );
}

// Luksusowy loading spinner
export function LuxurySpinner({ 
  size = 'md',
  goldAccent = true,
  className = '' 
}) {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };
  
  return (
    <motion.div
      className={`
        ${sizes[size]} rounded-full border-2 border-transparent
        ${goldAccent 
          ? 'border-t-enterprise-brown border-r-terra' 
          : 'border-t-charcoal border-r-ash'
        }
        ${className}
      `}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: "linear"
      }}
    />
  );
}

// Luksusowy floating action button
export function LuxuryFAB({ 
  children, 
  onClick,
  position = 'bottom-right', // 'bottom-right', 'bottom-left', 'top-right', 'top-left'
  goldAccent = true,
  className = '' 
}) {
  const positions = {
    'bottom-right': 'bottom-8 right-8',
    'bottom-left': 'bottom-8 left-8',
    'top-right': 'top-8 right-8',
    'top-left': 'top-8 left-8'
  };
  
  return (
    <motion.button
      className={`
        fixed z-50 w-14 h-14 rounded-full shadow-lg
        flex items-center justify-center
        ${goldAccent 
          ? 'bg-gradient-to-br from-enterprise-brown to-terra hover:from-terra hover:to-sand' 
          : 'bg-charcoal hover:bg-charcoal-light'
        }
        text-sanctuary transition-all duration-300
        ${positions[position]}
        ${className}
      `}
      whileHover={{ 
        scale: 1.1,
        boxShadow: goldAccent 
          ? '0 8px 25px rgba(193,155,104,0.4)' 
          : '0 8px 25px rgba(42,39,36,0.4)'
      }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{
        type: "spring",
        stiffness: 400,
        damping: 17
      }}
    >
      {children}
    </motion.button>
  );
}